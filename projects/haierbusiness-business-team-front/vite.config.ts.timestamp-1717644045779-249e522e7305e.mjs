// vite.config.ts
import { loadEnv } from "file:///D:/groupGitCode/haierbusiness/haierbusiness-front/node_modules/.pnpm/vite@4.4.9_@types+node@18.17.11_less@4.2.0_sass@1.69.5/node_modules/vite/dist/node/index.js";
import vue from "file:///D:/groupGitCode/haierbusiness/haierbusiness-front/node_modules/.pnpm/@vitejs+plugin-vue@4.3.3_vite@4.4.9_vue@3.3.4/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import path from "path";
var __vite_injected_original_dirname = "D:\\groupGitCode\\haierbusiness\\haierbusiness-front\\projects\\haierbusiness-business-team-front";
var CWD = process.cwd();
var vite_config_default = ({ mode }) => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD);
  // 生成版本信息插件
  const generateVersionPlugin = {
    name: 'generate-version',
    // 仅在构建时执行
    apply: 'build',
    closeBundle() {
      // 生成版本文件
      const versionInfo = {
        // 使用时间戳作为版本号
        version: Date.now(),
        buildTime: new Date().toISOString(),
        mode: mode
      };

      
            // 确保 dist 目录存在
            const distPath = path.resolve(__dirname, 'dist')
            if (!existsSync(distPath)) {
                mkdirSync(distPath, {recursive: true})
            }

            writeFileSync(
                path.resolve(__dirname, 'dist/version.json'),
                JSON.stringify(versionInfo, null, 2)
            );

    }
  };
  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        "~": path.resolve(__vite_injected_original_dirname, "./"),
        "@": path.resolve(__vite_injected_original_dirname, "./src")
      }
    },
    plugins: [vue(),generateVersionPlugin],
    build: {
      target: ["es2015"],
      sourcemap: true
    },
    server: {
      port: 5201,
      host: "0.0.0.0",
      proxy: {
        // http://************:9223/team/order/create
        // http://localhost:5201/hb/team/order/create
        // 本地开发
        // "/hb/team/api": {
        //   target: "http://************:9223",
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace(new RegExp(`/hb/team/api`), ''),
        // },
        "/hb": {
          target: "https://businessmanagement-test.haier.net/hbweb/index/hb",
          changeOrigin: true,
          rewrite: (path2) => path2.replace(/^\/hb/, "")
        },
        "/upload": {
          target: "https://businessmanagement-test.haier.net/hbweb/upload",
          changeOrigin: true,
          rewrite: (path2) => path2.replace(/^\/upload/, "")
        }
      }
    }
    // css: {
    //   postcss: {
    //     plugins: [
    //       postcssPx2remExclude({
    //         remUnit: 192, 
    //         // exclude: /mobile/i // 忽略node_modules目录下的文件
    //       })
    //     ]
    //   }
    // }
  };
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
