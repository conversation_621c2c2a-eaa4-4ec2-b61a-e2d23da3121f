<script setup lang="ts">
// 需求提报
import {message, Modal} from 'ant-design-vue';
import {nextTick, onBeforeUnmount, onMounted, ref, watch} from 'vue';
import {useRoute, useRouter} from 'vue-router';
import {marked} from 'marked';
import {
  delData,
  errorModal,
  getDataBy,
  meetingProcessOrchestration,
  numComputedArrMethod,
  resolveParam,
  routerParam,
  saveDataBy,
} from '@haierbusiness-front/utils';

import {aiApi, demandApi, miceBidManOrderListApi} from '@haierbusiness-front/apis';

import DemandBase from './components/DemandBase.vue';
import DemandChoose from './components/DemandChoose.vue';
import DemandPlan from './components/DemandPlan.vue';
import DemandMaterial from './components/DemandMaterial.vue';
import DemandTicket from './components/DemandTicket.vue';
import DemandGift from './components/DemandGift.vue';
import DemandOther from './components/DemandOther.vue';
import MeetingBudget from './components/MeetingBudget.vue';

import {
  DemandSubmitObj,
  HotelsArr,
  MaterialsArr,
  OthersArr,
  PresentsArr,
  ProcessOrchestrationServiceTypeEnum,
  TrafficsArr,
} from '@haierbusiness-front/common-libs';
import {aiAssistantStore} from '@/store/aiAssistantStore'; // 调整路径
import {storeToRefs} from 'pinia';
import {applicationStore} from '@haierbusiness-front/utils/src/store/applicaiton';
import {demandJsonAdd} from './demandData.js';

const {loginUser} = storeToRefs(applicationStore());

const store = aiAssistantStore();
const {miceRequestData} = storeToRefs(store);

const route = useRoute();
const router = useRouter();

const anchorList = ref<array>([
  {
    key: '01',
    href: '#demand-base',
    title: '01 基础需求',
  },
  {
    key: '02',
    href: '#demand-plan',
    title: '02 日程安排',
    children: [],
  },
]);

const demandBaseRef = ref(null);
const demandPlanRef = ref(null);
const demandChooseRef = ref(null);
const demandMaterialRef = ref(null);
const demandTicketRef = ref(null);
const demandGiftRef = ref(null);
const demandOtherRef = ref(null);
const demandBudgetRef = ref(null);
const spinLoading = ref<Boolean>(false);

const autoSave = ref(null); // 自动保存
const countdownTimer = ref(null); //
const countdownTime = ref<number>(60);

const orderSource = ref<String>(''); // 会议来源
const orderState = ref<String>(''); // 会议状态
const processNode = ref<String>(''); // 流程节点

const mainCode = ref<String>(''); // 会议单号
const miceId = ref<String>(''); // 会议id
const sourceId = ref<String>(''); // 历史id
const demandRejectReason = ref<String>(''); // 驳回原因
const demandChooseList = ref<Array>([]); // 需求模块
const hotelList = ref<Array>([]); // 基础需求-酒店列表

const demandBaseData = ref<DemandSubmitObj>({}); // 基础需求

const startDate = ref<String>(''); // 日程安排-开始时间
const endDate = ref<String>(''); // 日程安排-结束时间
const planList = ref<Array>([]); // 日程安排-列表

const cacheStr = ref<String>(''); // 缓存获取数据
const DemandParams = ref<DemandSubmitObj>({}); // 需求提报
const autoSaveParams = ref<DemandSubmitObj>({}); // 暂存

const material = ref<MaterialsArr>({}); // 布展物料
const traffic = ref<TrafficsArr>({}); // 票务预定
const presents = ref<PresentsArr>([]); // 礼品需求
const others = ref<PresentsArr>([]); // 其他需求

const calcTotalPrice = ref(null); // 测算总金额 ,传入后和后端计算金额做校验
const demandTotalPrice = ref(null); // 需求总金额
const isCalcPass = ref(false); // 是否验证通过

const isUrgent = ref(false); // 是否加急

const preLoading = ref(false); // 预览
const subLoading = ref(false); // 提交

const businessIndex = import.meta.env.VITE_BUSINESS_INDEX_URL + '#';
const businessMiceBidman = import.meta.env.VITE_BUSINESS_MICE_BIDMAN_URL + '#';

const aiModalShow = ref<Boolean>(false);
const aiItemStr = ref<String>('');

const demandSets = ref<Array>([]); // 需求配置
const isQingdao = ref<string>(''); // 是否固定会议城市仅青岛
const meetingMinDaysNum = ref<number>(0); // 会议最短召开日期(单位: 天)
const isHotelDemandSubmittable = ref<Boolean>(false); // 是否可提报多酒店需求
const supportInternational = ref<Boolean>(false); // 是否支持国际会议提报

const isShowDel = ref<boolean>(true); // 展示删除按钮
const isShowAiFillIn = ref<boolean>(false); // 展示智能填报按钮

watch(hotelList, (newHotelList) => {
  if (newHotelList && newHotelList.length > 0) {
    isShowAiFillIn.value = true;
  }
}, {immediate: true, deep: true});

// 基础需求
const demandBaseFunc = (baseObj: DemandSubmitObj) => {
  demandBaseData.value = {...baseObj};
};

// 基础需求-酒店列表
const demandHotelFunc = (listArr: HotelsArr) => {
  hotelList.value = [...listArr];
};

// 需求模块
const demandChooseFunc = (chooseArr: Array) => {
  // 布展物料-material
  // 票务预定-ticket
  // 礼品需求-gift
  // 其他需求-other

  demandChooseList.value = [...chooseArr];

  // 锚点
  let demandList = [];
  demandChooseList.value.forEach((e) => {
    const anchorTitle =
        e === 'material' ? '布展物料' : e === 'ticket' ? '票务预定' : e === 'gift' ? '礼品需求' : '其他需求';

    demandList.push({
      key: 'demand-' + e,
      href: '#demand-' + e,
      title: '0' + (demandList.length + 3) + ' ' + anchorTitle,
    });
  });

  demandList.push({
    key: 'meeting-budget',
    href: '#meeting-budget',
    title: '0' + (demandList.length + 3) + ' 会议估算',
  });

  const twoAnchorList = [anchorList.value[0], anchorList.value[1]];

  anchorList.value = [...twoAnchorList, ...demandList];
};

// 日程安排
const demandPlanFunc = (planObj: DemandSubmitObj) => {
  startDate.value = planObj.startDate;
  endDate.value = planObj.endDate;
  planList.value = planObj.planList;

  // 锚点
  let dateList = [];
  planObj.planList?.forEach((e) => {
    dateList.push({key: e.demandDate, href: '#' + e.demandDate, title: '- ' + e.demandDate});
    anchorList.value[1].children = [...dateList];
  });
};

// 布展物料
const demandMaterialsFunc = (obj: MaterialsArr) => {
  material.value = obj;
};

// 票务预定
const demandTrafficFunc = (obj: TrafficsArr) => {
  traffic.value = obj;
};

// 礼品需求
const demandPresentFunc = (obj: DemandSubmitObj) => {
  presents.value = obj.presents;
};

// 其他需求
const demandOtherFunc = (obj: OthersArr) => {
  others.value = obj.others;
};

// 会议估算
const demandBudgetFunc = async (obj: DemandSubmitObj, callback: () => {}) => {
  // 点击会务预算按钮
  if (obj.isCalcBtnClick) {
    // 价格校验
    await demandSub('calc');

    if (isCalcPass.value) {
      // 预算效果
      callback();
    }
  } else {
    calcTotalPrice.value = obj.calcTotalPrice || obj.demandTotalPrice; // 测算总金额 ,传入后和后端计算金额做校验
    demandTotalPrice.value = obj.demandTotalPrice; // 需求总金额
  }
};
// 查看估算结果
const viewCalc = () => {
  demandTemporarily('auto', 'previewCalc');
};

// 锚点
const handleClick = (e: any) => {
  e.preventDefault();
};

// AI智能填报
const AIFilling = () => {
  // TODO
  console.log('%c [ AI智能填报 ]-81', 'font-size:13px; background:pink; color:#bf2c9f;', 'AI智能填报');
};

// 智能填报
const demandJsonAddBtn = () => {
  aiModalShow.value = true;
};
const handleAiBtn = () => {
  let jsonOutput = {};
  let isJson = true;

  try {
    jsonOutput = JSON.parse(aiItemStr.value);
  } catch (error) {
    message.error('Invalid JSON');
    isJson = false;
  }

  if (!isJson) return;
  cancelAiBtn();
  reloadFormData(jsonOutput);
};
const cancelAiBtn = () => {
  aiItemStr.value = '';
  aiModalShow.value = false;
};
const copyAiBtn = () => {
  aiItemStr.value = JSON.stringify(demandJsonAdd);
};
const aiSelectedItems = ref<Array>(['stays', 'caterings', 'places']); // 默认选中部分项目
let autoAiFillLoading = false
const autoAiFillBtn = async () => {

    autoAiFillLoading = true
    await demandTemporarily('auto', 'aiFill');
    const requestMessage = `请填写表单，
  会议开始时间：${startDate.value}，
  会议结束时间：${endDate.value}，
  需要填写以下项目：：${aiSelectedItems.value.join('、')}，
  原始内容： ${JSON.stringify({
      ...demandBaseData.value,
      hotels: [...hotelList.value]
    })}，
  在原始内容上追加，并新增项目每天都要填写`;

    console.log(requestMessage)
    let aiResult = {
      errorContent: '',
      thinkingContent: '',
      dialogueContent: '',
      miceRequest: null
    }
    aiApi.executeAgent(
        {
          message: requestMessage,
          chatId: "2",
          enableMessageCheck: false,
          serviceName: 'agentMiceSmartFormsServiceImpl',
        },
        (content) => {
          nextTick(() => {
            if (content.errorContent) {
              aiResult.errorContent = content.errorContent;
            }
            if (content.thinkingContent && content.thinkingContent != 'null') {
              aiResult.thinkingContent += marked(aiResult.thinkingContent);
            }
            if (content.dialogueContent) {
              aiResult.dialogueContent = marked(aiResult.dialogueContent);
            }
            if (content.miceRequest) {
              aiResult.miceRequest = content.miceRequest
            }
          });
        },
        () => {
          console.log(aiResult)
          autoAiFillLoading = false
          aiItemStr.value = JSON.stringify(aiResult.miceRequest);
        },
        (err) => {

        }
    );


};
// 重新加载数据
const reloadFormData = (formData) => {
  spinLoading.value = true;
  subLoading.value = true;
  preLoading.value = true;

  setTimeout(() => {
    const cacheData = JSON.parse(cacheStr.value);
    let AiData = {...formData};

    if (cacheData.miceName) {
      AiData.miceName = cacheData.miceName;
    }
    if (cacheData.contactUserName) {
      AiData.contactUserName = cacheData.contactUserName;
      AiData.contactUserCode = cacheData.contactUserCode;
      AiData.contactUserPhone = cacheData.contactUserPhone;
      AiData.contactUserEmail = cacheData.contactUserEmail;
    }
    if (cacheData.miceType !== '' && cacheData.miceType !== null && cacheData.miceType >= 0) {
      AiData.miceType = cacheData.miceType;
    }
    if (cacheData.personTotal !== '' && cacheData.personTotal !== null && cacheData.personTotal >= 0) {
      AiData.personTotal = cacheData.personTotal;
    }

    cacheStr.value = JSON.stringify(AiData);

    spinLoading.value = false;
    subLoading.value = false;
    preLoading.value = false;
  }, 300);
};
// 监听AI数据变化
watch(miceRequestData, (newData) => {
  if (newData) {
    reloadFormData(newData);
  }
});
// 预览
const demandPreview = () => {
  demandTemporarily('auto', 'preview');
};

// 暂存
const demandTemporarily = async (type, isPreview = '') => {
  if (isPreview === 'preview' || isPreview === 'previewCalc') {
    // 需求预览
    preLoading.value = true;
  }

  // 基础需求
  await demandBaseRef?.value.tempSave();
  // 需求模块
  await demandChooseRef?.value.tempSave();
  // 每日计划
  await demandPlanRef?.value.tempSave();
  if (demandChooseList.value.includes('material')) {
    // 布展物料
    await demandMaterialRef?.value.tempSave();
  }
  if (demandChooseList.value.includes('ticket')) {
    // 票务预定
    await demandTicketRef?.value.tempSave();
  }
  if (demandChooseList.value.includes('gift')) {
    // 礼品
    await demandGiftRef?.value.tempSave();
  }
  if (demandChooseList.value.includes('other')) {
    // 其他
    await demandOtherRef?.value.tempSave();
  }
  // 会务预算
  await demandBudgetRef?.value.tempSave();

  if (!miceId.value) {
    message.error('会议id不存在！');
    return;
  }

  autoSaveParams.value = {};

  let stays = []; // 住宿
  let places = []; // 会场
  let caterings = []; // 用餐
  let vehicles = []; // 用车
  let attendants = []; // 服务人员
  let activities = []; // 活动
  let insurances = []; // 保险

  planList.value.forEach((e) => {
    stays = [...stays, ...e.stays];
    places = [...places, ...e.places];
    caterings = [...caterings, ...e.caterings];
    vehicles = [...vehicles, ...e.vehicles];
    attendants = [...attendants, ...e.attendants];
    activities = [...activities, ...e.activities];
    insurances = [...insurances, ...e.insurances];
  });

  autoSaveParams.value = {
    mainCode: mainCode.value,
    demandRejectReason: demandRejectReason.value,
    ...demandBaseData.value,
    hotels: [...hotelList.value],
    startDate: startDate.value,
    endDate: endDate.value,
    stays: [...stays],
    places: [...places],
    caterings: [...caterings],
    vehicles: [...vehicles],
    attendants: [...attendants],
    activities: [...activities],
    insurances: [...insurances],

    calcTotalPrice: calcTotalPrice.value,
    demandTotalPrice: demandTotalPrice.value,
  };

  autoSaveParams.value.material = demandChooseList.value.includes('material') ? {...material.value} : {};
  autoSaveParams.value.traffic = demandChooseList.value.includes('ticket') ? {...traffic.value} : {};
  autoSaveParams.value.presents = demandChooseList.value.includes('gift') ? [...presents.value] : [];
  autoSaveParams.value.others = demandChooseList.value.includes('other') ? [...others.value] : [];

  console.log('%c [ 暂存 ]-156', 'font-size:13px; background:pink; color:#bf2c9f;', autoSaveParams.value);

  // 后端缓存
  const res = await saveDataBy({
    applicationCode: 'haierbusiness-mice-bid',
    // 规则: haierbusiness-mice-bid_工号_你业务的缓存key
    cacheKey: 'haierbusiness-mice-bid_' + loginUser.value?.username + '_demandSubKey' + miceId.value, // 需求提报
    cacheValue: JSON.stringify({
      ...autoSaveParams.value,
    }),
  });

  if (res && type === 'hand') {
    // 手动保存
    message.success('需求提报已暂存！');
  }

  if (isPreview === 'preview' || isPreview === 'previewCalc') {
    // 需求预览
    preLoading.value = false;
    const params = {
      ...resolveParam(route.query.record),
      miceId: miceId.value,
      previewCalc: isPreview,
      demandTotalPrice: demandTotalPrice.value,
      priceSource: 'user',
      hideBtn: '1',
    };

    router.push({
      path: '/demand/demandPreview',
      query: {
        record: routerParam(params),
      },
    });
  }
};

// 缓存查询
const getCache = async () => {
  if (!miceId.value) {
    return;
  }

  spinLoading.value = true;

  let resStr = '';

  const resCacheStr = await getDataBy({
    applicationCode: 'haierbusiness-mice-bid',
    cacheKey: 'haierbusiness-mice-bid_' + loginUser.value?.username + '_demandSubKey' + miceId.value, // 需求提报
  });
  resStr = resCacheStr;

  if (!resStr) {
    // 页面来源 - user:用户端  manage:后台管理
    if (
        (orderState.value == 110 || orderState.value == 120 || processNode.value === 'DEMAND_PRE_INTERACT') &&
        (orderSource.value === 'manage' || orderSource.value === 'user')
    ) {
      // 订单状态110，驳回 - 查询详情接口
      // 流程节点 - DEMAND_PRE_INTERACT - 需求事先交互 - 查询详情接口

      // 提报详情
      getDetails();
      return;
    } else {
      // 若没有获取到数据
      const routerData = {
        miceName: '',
        startDate: '',
        endDate: '',
        districtType: null, // 会议地点
        personTotal: null, // 会议人数
        miceType: null, // 会议类型
        hotels: [], // 酒店列表
      };

      resStr = JSON.stringify(routerData);
    }
  }

  console.log(
      '%c [ 缓存查询 ]-171114',
      'font-size:13px; background:pink; color:#bf2c9f;',
      resStr ? JSON.parse(resStr) : resStr,
  );

  cacheStr.value = resStr || '';

  if (cacheStr.value) {
    const cacheObj = JSON.parse(cacheStr.value);
    mainCode.value = cacheObj?.mainCode;
    demandRejectReason.value = cacheObj?.demandRejectReason || '';
  }

  spinLoading.value = false;

  // 1min自动保存
  countDownOneMin();
};

// 获取会议详情
const getDetails = async () => {
  if (!miceId.value) {
    return;
  }

  spinLoading.value = true;
  let res = {};

  if (orderSource.value === 'manage') {
    // 管理端-订单详情
    res = await miceBidManOrderListApi.platformDetails({
      miceId: miceId.value,
    });
  } else {
    // 用户端-订单详情
    res = await miceBidManOrderListApi.userDetails({miceId: miceId.value});
  }

  console.log('%c [ 获取会议详情 ]-497', 'font-size:13px; background:pink; color:#bf2c9f;', res);

  // 酒店id赋值
  res?.hotels.forEach((e) => {
    e.tempDemandHotelId = e.id;
  });

  // 住宿
  res?.stays.forEach((e) => {
    e.tempDemandHotelId = e.miceDemandHotelId;
  });

  // 会场
  res?.places.forEach((e) => {
    e.tempDemandHotelId = e.miceDemandHotelId;
  });

  // 用餐
  res?.caterings.forEach((e) => {
    e.tempDemandHotelId = e.miceDemandHotelId;
  });

  cacheStr.value = JSON.stringify(res) || '';
  mainCode.value = res.mainCode;
  if (res.processNode === res.reverseProcessNode || res.processNode === res.reverseAfterProcessNode) {
    demandRejectReason.value = res?.demandRejectReason || '';
  }

  spinLoading.value = false;

  countDownOneMin();
};

// 1min自动保存
const countDownOneMin = () => {
  autoSave.value = setInterval(() => {
    demandTemporarily('auto');
  }, 60000);

  countdownTimer.value = setInterval(() => {
    countdownTime.value = countdownTime.value === 0 ? 60 : countdownTime.value - 1;
  }, 1000);
};

// 缓存删除
const delCache = async () => {
  if (!miceId.value) {
    message.error('会议id不存在！');
    return;
  }
  delData({
    applicationCode: 'haierbusiness-mice-bid',
    cacheKey: 'haierbusiness-mice-bid_' + loginUser.value?.username + '_demandSubKey' + miceId.value, // 需求提报
  });
};

// 锚点 - 用户点击添加需求，需跳转至添加位置
const anchorId = (id: string) => {
  document?.getElementById(id)?.scrollIntoView({
    behavior: 'smooth', //smooth:平滑，auto：直接定位
    block: 'center',
    inline: 'start',
  });
};

// 提交
const demandSub = async (type: String) => {
  subLoading.value = true;
  // 是否验证通过
  isCalcPass.value = false;

  DemandParams.value = {
    miceId: miceId.value,
  };

  /*
      基础需求
    */
  let isVerifyPassed = await demandBaseRef?.value.onSubmit();
  if (!isVerifyPassed) {
    subLoading.value = false;
    message.error('请填写基础需求！');

    return;
  }
  // 基础需求 - 赋值
  DemandParams.value = {...DemandParams.value, ...demandBaseData.value, hotels: [...hotelList.value]};
  // end

  /*
      日程安排
    */
  if (startDate.value == '') {
    subLoading.value = false;
    message.error('请选择日期！');
    return;
  }

  isVerifyPassed = await demandPlanRef?.value.onSubmitPlan();
  if (!isVerifyPassed) {
    subLoading.value = false;
    // message.error('请填写日程安排！');

    return;
  }

  let stays = []; // 住宿
  let places = []; // 会场
  let caterings = []; // 用餐
  let vehicles = []; // 用车
  let attendants = []; // 服务人员
  let activities = []; // 活动
  let insurances = []; // 保险

  planList.value.forEach((e) => {
    if (e.stays && e.stays.length > 0) {
      e.stays.forEach((j) => {
        stays.push(j);
      });
    }
    if (e.places && e.places.length > 0) {
      e.places.forEach((j) => {
        places.push(j);
      });
    }
    if (e.caterings && e.caterings.length > 0) {
      e.caterings.forEach((j) => {
        caterings.push(j);
      });
    }
    if (e.vehicles && e.vehicles.length > 0) {
      e.vehicles.forEach((j) => {
        vehicles.push(j);
      });
    }
    if (e.attendants && e.attendants.length > 0) {
      e.attendants.forEach((j) => {
        attendants.push(j);
      });
    }
    if (e.activities && e.activities.length > 0) {
      e.activities.forEach((j) => {
        activities.push(j);
      });
    }
    if (e.insurances && e.insurances.length > 0) {
      e.insurances.forEach((j) => {
        insurances.push(j);
      });
    }
  });

  let planParams = {};
  if (stays && stays.length > 0) {
    planParams.stays = stays;
  }
  if (places && places.length > 0) {
    planParams.places = places;
  }
  if (caterings && caterings.length > 0) {
    planParams.caterings = caterings;
  }
  if (vehicles && vehicles.length > 0) {
    planParams.vehicles = vehicles;
  }
  if (attendants && attendants.length > 0) {
    planParams.attendants = attendants;
  }
  if (activities && activities.length > 0) {
    planParams.activities = activities;
  }
  if (insurances && insurances.length > 0) {
    planParams.insurances = insurances;
  }

  // 日程安排 - 赋值
  DemandParams.value = {...DemandParams.value, startDate: startDate.value, endDate: endDate.value, ...planParams};
  // end

  /*
      布展物料
    */
  if (demandChooseList.value.includes('material')) {
    isVerifyPassed = await demandMaterialRef?.value.onSubmit();
    if (!isVerifyPassed) {
      subLoading.value = false;
      message.error('请填写布展物料！');

      return;
    }

    DemandParams.value = {...DemandParams.value, material: {...material.value}};
  } else {
    delete DemandParams.value.material;
  }
  // end

  /*
      票务预定
    */
  if (demandChooseList.value.includes('ticket')) {
    isVerifyPassed = await demandTicketRef?.value.onSubmit();
    if (!isVerifyPassed) {
      subLoading.value = false;
      message.error('请填写票务预定！');

      return;
    }

    DemandParams.value = {...DemandParams.value, traffic: {...traffic.value}};
  } else {
    delete DemandParams.value.traffic;
  }
  // end

  /*
      礼品需求
    */
  if (demandChooseList.value.includes('gift')) {
    isVerifyPassed = await demandGiftRef?.value.onSubmit();
    if (!isVerifyPassed) {
      subLoading.value = false;
      message.error('请填写礼品需求！');

      return;
    }

    DemandParams.value = {...DemandParams.value, presents: [...presents.value]};
  } else {
    delete DemandParams.value.presents;
  }
  // end

  /*
      其他需求
    */
  if (demandChooseList.value.includes('other')) {
    isVerifyPassed = await demandOtherRef?.value.onSubmit();
    if (!isVerifyPassed) {
      subLoading.value = false;
      message.error('请填写其他需求！');

      return;
    }

    DemandParams.value = {...DemandParams.value, others: [...others.value]};
  } else {
    delete DemandParams.value.others;
  }
  // end

  // 会务预算
  await demandBudgetRef?.value.tempSave();

  //
  // 需求提报时候不能各分项人数不能超会议总人数
  //
  let overPersonArr = [];

  // 住宿人数
  if (DemandParams.value?.stays && DemandParams.value?.stays.length > 0) {
    overPersonArr = DemandParams.value.stays.filter((e) => e.personNum > demandBaseData.value.personTotal);
  }
  if (overPersonArr.length > 0) {
    subLoading.value = false;
    message.error(overPersonArr[0].demandDate + '，住宿人数超出会议人数！');
    return;
  }

  // 会场人数
  if (DemandParams.value?.places && DemandParams.value?.places.length > 0) {
    overPersonArr = DemandParams.value.places.filter((e) => e.personNum > demandBaseData.value.personTotal);
  }
  if (overPersonArr.length > 0) {
    subLoading.value = false;
    message.error(overPersonArr[0].demandDate + '，会场参会人数超出会议人数！');
    return;
  }

  // 用餐
  if (DemandParams.value?.caterings && DemandParams.value?.caterings.length > 0) {
    overPersonArr = DemandParams.value.caterings.filter((e) => e.personNum > demandBaseData.value.personTotal);
  }
  if (overPersonArr.length > 0) {
    subLoading.value = false;
    message.error(overPersonArr[0].demandDate + '，用餐人数超出会议人数！');
    return;
  }

  // 拓展活动
  if (DemandParams.value?.activities && DemandParams.value?.activities.length > 0) {
    overPersonArr = DemandParams.value.activities.filter((e) => e.personNum > demandBaseData.value.personTotal);
  }
  if (overPersonArr.length > 0) {
    subLoading.value = false;
    message.error(overPersonArr[0].demandDate + '，拓展活动参与人数超出会议人数！');
    return;
  }

  // 保险
  if (DemandParams.value?.insurances && DemandParams.value?.insurances.length > 0) {
    overPersonArr = DemandParams.value.insurances.filter((e) => e.personNum > demandBaseData.value.personTotal);
  }
  if (overPersonArr.length > 0) {
    subLoading.value = false;
    message.error(overPersonArr[0].demandDate + '，保险参保人数超出会议人数！');
    return;
  }

  /*
      会议估算
    */
  if (type === 'calc') {
    // 会议估算 - 校验
    await demandBudgetRef.value.calcFn(DemandParams.value); // 价格测算

    subLoading.value = false;
    isCalcPass.value = true;
    return;
  }

  if (demandTotalPrice.value === null || demandTotalPrice.value === undefined) {
    subLoading.value = false;
    // message.error('请进行会务预算测算！');
    message.error('请填写会务估算！');

    anchorId('meeting-budget');
    return;
  }

  DemandParams.value = {
    ...DemandParams.value,
    calcTotalPrice: calcTotalPrice.value || demandTotalPrice.value,
    demandTotalPrice: demandTotalPrice.value,

    removeByKey: 'demandSubKey' + miceId.value,
    demandRejectReason: demandRejectReason.value || null,
  };

  // end

  if (demandTotalPrice.value > 999999999) {
    subLoading.value = false;
    message.error('当前预计投入金额过大，请修改预计投入金额！');
    return;
  }

  Modal.confirm({
    title: '确定提交？',
    // icon: null,
    content: '',
    onOk() {
      subLoading.value = false;
      demandSubFunc();
    },
    onCancel() {
      subLoading.value = false;
    },
  });
};

// 提交
const demandSubFunc = async () => {
  subLoading.value = true;

  let res = {};

  const transformSpecsFields = (data) => {
    const transform = (obj) => {
      if (Array.isArray(obj)) {
        return obj.map((item) => transform(item));
      } else if (obj && typeof obj === 'object') {
        const transformed = {...obj};

        // ['ledSpecs', 'personSpecs', 'specs'].forEach((field) => {
        //   if (transformed[field] === '以服务商提报为准') {
        //     transformed[field] = null;
        //   }
        // });

        Object.keys(transformed).forEach((key) => {
          if (transformed[key] && typeof transformed[key] === 'object') {
            transformed[key] = transform(transformed[key]);
          }
        });

        return transformed;
      }
      return obj;
    };

    return transform(data);
  };

  const transformedParams = transformSpecsFields(DemandParams.value);

  if (orderSource.value === 'manage' && processNode.value === 'DEMAND_PRE_INTERACT') {
    // 需求互动提报
    res = await demandApi.demandPlatformSubmit({...transformedParams, sourceId: sourceId.value}, (error) => {
      subLoading.value = false;
      errorModal(error?.message);
    });
  } else {
    // 需求提报
    let params = {...transformedParams};

    if (processNode.value === 'DEMAND_SUBMIT') {
      params.sourceId = sourceId.value;
    }

    res = await demandApi.demandUserSubmit({...params}, (error) => {
      subLoading.value = false;
      errorModal(error?.message);
    });
  }

  subLoading.value = false;

  if (res.data) {
    // 缓存删除
    delCache();

    message.success('需求提报成功！');

    // 页面来源 - user:用户端  manage:后台管理
    if (orderSource.value === 'manage') {
      // 订单列表 - 后台管理
      const url = businessMiceBidman + '/bidman/orderList/index';
      window.location.replace(url);
    } else {
      // 订单列表 - 用户端
      const url = businessIndex + '/card-order/miceOrder';
      window.location.replace(url);
    }
  }
};

// 流程详情
const getProcessDetails = async (processId = localStorage.getItem('processId') || '', verId = '') => {
  // 流程ID
  if (!processId) {
    message.error('流程ID不存在！');
    return;
  }

  const res = await miceBidManOrderListApi.processDetails({
    id: processId,
    verId: verId,
  });

  // 需求配置
  const demandProcessList = ProcessOrchestrationServiceTypeEnum.getTypeOptions().map((e) => {
    return e.value;
  });
  demandSets.value = numComputedArrMethod(res.items, [...demandProcessList]);

  // 是否固定会议城市仅青岛
  isQingdao.value = meetingProcessOrchestration(
      'DEMAND_SUBMIT',
      res.nodes || [],
      'demandSubmitFixedCityValueConfigDefine',
  );

  // 会议最短召开日期(单位: 天)
  const overDay = meetingProcessOrchestration(
      'DEMAND_SUBMIT',
      res.nodes || [],
      'demandSubmitMinimumMiceStartDateConfigDefine',
  );
  meetingMinDaysNum.value = overDay ? Number(overDay) : 0;

  // 是否可提报多酒店需求
  isHotelDemandSubmittable.value = res.isHotelDemandSubmittable;

  // 是否支持国际会议提报
  supportInternational.value = res.supportInternational;
};

const delCacheBtn = async () => {
  await delCache();
  window.location.reload();
};

onMounted(async () => {
  const record = resolveParam(route.query.record);
  console.log('%c [ record ]-606', 'font-size:13px; background:pink; color:#bf2c9f;', record);
  mainCode.value = record.mainCode;
  miceId.value = record.miceId;
  orderSource.value = record.orderSource || '';
  orderState.value = record.orderState || '';
  sourceId.value = record.demandId || '';

  // DEMAND_SUBMIT - 需求提报
  // DEMAND_PRE_INTERACT - 需求事先交互
  processNode.value = record.processNode || 'DEMAND_SUBMIT';

  isShowDel.value = localStorage.getItem('testProcessSignForCiCi') === '1';

  // 流程详情
  await getProcessDetails(record.pdMainId, record.pdVerId);

  // 缓存查询
  await getCache();
});

onBeforeUnmount(() => {
  clearInterval(autoSave.value);
  clearInterval(countdownTimer.value);
});
</script>

<template>
  <!-- 需求提报 -->
  <div class="demand_container">
    <div class="wid1280">
      <div class="demand_head">
        <div class="demand_head_title">
          <div class="demand_img mr16"></div>
          <div>需求提报</div>
        </div>
        <div class="demand_head_order_num">
          <div>会议单号</div>
          <div>{{ mainCode }}</div>
        </div>
      </div>
      <a-spin :spinning="spinLoading || subLoading || preLoading">
        <div v-if="spinLoading" style="height: 100vh"></div>
        <div v-else>
          <a-alert
              v-if="demandRejectReason"
              class="mb16 demand_reject_reason"
              message="驳回原因："
              :description="demandRejectReason"
              show-icon
              type="warning"
          />
          <!-- 基础需求 -->
          <demand-base
              id="demand-base"
              ref="demandBaseRef"
              :cache-str="cacheStr"
              :isQingdao="isQingdao"
              :isHotelDemandSubmittable="isHotelDemandSubmittable"
              :supportInternational="supportInternational"
              @demandHotelFunc="demandHotelFunc"
              @demandBaseFunc="demandBaseFunc"
          />
          <!-- 需求模块 -->
          <a-affix :offset-top="78" class="mt16">
            <demand-choose
                ref="demandChooseRef"
                :cache-str="cacheStr"
                :demandSets="demandSets"
                @demandChooseFunc="demandChooseFunc"
            />
          </a-affix>
          <!-- 日程安排 -->
          <demand-plan
              id="demand-plan"
              ref="demandPlanRef"
              class="mt16"
              :meetingPersonTotal="demandBaseData.personTotal"
              :cache-str="cacheStr"
              :hotel-list="hotelList"
              :meetingMinDaysNum="meetingMinDaysNum"
              :demandSets="demandSets"
              @demandPlanFunc="demandPlanFunc"
          />
          <!-- 布展物料 -->
          <demand-material
              v-if="demandChooseList.includes('material')"
              id="demand-material"
              ref="demandMaterialRef"
              class="mt16"
              :cache-str="cacheStr"
              @demandMaterialsFunc="demandMaterialsFunc"
          />
          <!-- 票务预定 -->
          <demand-ticket
              v-if="demandChooseList.includes('ticket')"
              id="demand-ticket"
              ref="demandTicketRef"
              class="mt16"
              :cache-str="cacheStr"
              @demandTrafficFunc="demandTrafficFunc"
          />
          <!-- 礼品需求 -->
          <demand-gift
              v-if="demandChooseList.includes('gift')"
              id="demand-gift"
              ref="demandGiftRef"
              class="mt16"
              :cache-str="cacheStr"
              @demandPresentFunc="demandPresentFunc"
          />
          <!-- 其他需求 -->
          <demand-other
              v-if="demandChooseList.includes('other')"
              id="demand-other"
              ref="demandOtherRef"
              class="mt16"
              :cache-str="cacheStr"
              @demandOtherFunc="demandOtherFunc"
          />
          <!-- 会议估算 -->
          <meeting-budget
              id="meeting-budget"
              ref="demandBudgetRef"
              class="mt16 mb24"
              :cache-str="cacheStr"
              :demand-params="DemandParams"
              @demandBudgetFunc="demandBudgetFunc"
              @viewCalc="viewCalc"
          />
        </div>
      </a-spin>
    </div>
    <!-- 提交 -->
    <div v-show="miceId" class="sub_fixed">
      <div class="wid1280 flex_between">
        <div class="sub_auto_save">
          <div v-show="countdownTime === 0" class="auto_save_img"></div>
          <div class="auto_save_time pl5">
            {{ countdownTime === 0 ? '已自动保存' : countdownTime + 's后自动保存' }}
          </div>
        </div>
        <!-- <div class="sub_ai_write" @click="AIFilling">AI智能填报</div> -->
        <div class="sub_btns">
          <a-spin :spinning="spinLoading || subLoading || preLoading">
            <a-button v-if="isShowDel" class="mr8" danger @click="delCacheBtn()">缓存删除</a-button>
            <a-button
                v-if="isShowAiFillIn"
                class="mr8 ai-tech-btn"
                @click="demandJsonAddBtn()"
            >
              <div class="ai-btn-content">
                <span class="ai-btn-text" style="font-weight: 700">AI</span>
                <span class="ai-btn-text">智能填报</span>
                <div class="ai-btn-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"
                          fill="currentColor"/>
                    <path d="M19 12L19.91 15.19L23 16L19.91 16.81L19 20L18.09 16.81L15 16L18.09 15.19L19 12Z"
                          fill="currentColor"/>
                    <path d="M5 12L5.91 15.19L9 16L5.91 16.81L5 20L4.09 16.81L1 16L4.09 15.19L5 12Z"
                          fill="currentColor"/>
                  </svg>
                </div>
              </div>
            </a-button>
            <a-button class="mr8" :loading="preLoading" @click="demandPreview()">预览</a-button>
            <a-button class="mr8" @click="demandTemporarily('hand')">暂存</a-button>
            <a-button type="primary" :loading="subLoading" @click="demandSub">提交</a-button>
          </a-spin>
        </div>
      </div>
    </div>

    <!-- 锚点 -->
    <div class="anchor_fixed">
      <a-anchor affix :target-offset="200" :bounds="200" :offsetTop="100" @click="handleClick" :items="anchorList"/>
    </div>

    <!-- 智能填报 - 弹窗 -->
    <a-modal v-model:open="aiModalShow" title="智能填报" width="70%" :maskClosable="false">
      <!-- 添加选择项目 -->
      <div style="margin: 24px 0 16px">
        <div style="font-weight: 500; margin-bottom: 12px">选择要生成的项目：</div>
        <a-checkbox-group v-model:value="aiSelectedItems" style="width: 100%;">
          <a-row>
            <a-col :span="6">
              <a-checkbox value="stays">住宿</a-checkbox>
            </a-col>
            <a-col :span="6">
              <a-checkbox value="caterings">餐饮</a-checkbox>
            </a-col>
            <a-col :span="6">
              <a-checkbox value="places">会场</a-checkbox>
            </a-col>
            <a-col :span="6">
              <a-checkbox value="vehicles">用车</a-checkbox>
            </a-col>
            <a-col :span="6">
              <a-checkbox value="attendants">服务人员</a-checkbox>
            </a-col>
            <a-col :span="6">
              <a-checkbox value="activities">活动</a-checkbox>
            </a-col>
            <a-col :span="6">
              <a-checkbox value="insurances">保险</a-checkbox>
            </a-col>
          </a-row>
        </a-checkbox-group>
      </div>
      <a-textarea
          style="margin: 24px 0 36px"
          v-model:value="aiItemStr"
          placeholder="智能填报"
          :autoSize="{ minRows: 12 }"
          showCount
          allow-clear
      />

      <template #footer>
        <a-button class="mr8" :loading="autoAiFillLoading" @click="autoAiFillBtn('hand')">AI生成</a-button>
        <a-button class="mr8" @click="copyAiBtn('hand')">复制预制数据</a-button>
        <a-button class="mr8" @click="cancelAiBtn('hand')">取消</a-button>
        <a-button type="primary" @click="handleAiBtn()">确定</a-button>
      </template>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.demand_container {
  min-height: calc(100vh - 77px);
  padding-bottom: 60px;

  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;

  background: #f1f2f6;

  .demand_head {
    padding: 36px 0 24px;
    display: flex;
    justify-content: space-between;

    .demand_head_title {
      font-weight: 500;
      font-size: 28px;
      color: #1d2129;
      line-height: 40px;
      display: flex;

      .demand_img {
        width: 40px;
        height: 40px;
        background: url('@/assets/image/demand/demandIcon.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }

    .demand_head_order_num {
      text-align: right;
      line-height: 20px;
      color: #4e5969;
    }
  }

  .demand_reject_reason {
    padding: 24px;
    border-radius: 12px;
  }

  .sub_fixed {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 56px !important;
    line-height: 56px;
    background: #ffffff;
    box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.03);
    filter: blur(0px);
    border-top: 1px solid #e8e8e8;

    .flex_between {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .sub_auto_save {
        display: flex;

        color: #4e5969;
        line-height: 20px;

        .auto_save_img {
          width: 18px;
          height: 18px;
          background: url('@/assets/image/demand/right_green.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }

        .auto_save_time {
          text-align: right;
        }
      }

      .sub_ai_write {
        width: 132px;
        height: 36px;
        background: url('@/assets/image/demand/demand_ai_bgc.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        text-align: center;
        line-height: 36px;

        font-family: YouSheBiaoTiHei;
        font-size: 20px;
        color: #ffffff;
        cursor: pointer;
      }

      .sub_btns {
      }
    }
  }

  .anchor_fixed {
    position: fixed;
    top: 200px;
    left: 12px;
    bottom: 100px;

    /* height: calc(100vh - 300px); */
    overflow-y: auto;
    background: #f1f2f6;
    border-radius: 12px;

    /* 锚点 */

    :deep(.ant-anchor-wrapper) {
      padding-right: 12px;
      height: calc(100vh - 300px);
      overflow-y: auto;
      max-width: 150px;
    }

    :deep(.ant-anchor-wrapper:not(.ant-anchor-wrapper-horizontal) .ant-anchor::before) {
      border-inline-start: none;
    }

    :deep(.ant-anchor-wrapper:not(.ant-anchor-wrapper-horizontal) .ant-anchor .ant-anchor-ink) {
      width: 0;
    }

    :deep(.ant-anchor-wrapper .ant-anchor .ant-anchor-link) {
      padding-block: 12px;
      /* padding-top: 24px; */
      padding-inline: 12px 0;
    }

    :deep(.ant-anchor-wrapper .ant-anchor .ant-anchor-link-title) {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #595959;
      line-height: 20px;
    }

    :deep(.ant-anchor-wrapper .ant-anchor .ant-anchor-link-active > .ant-anchor-link-title) {
      color: #1868db;
    }
  }
}

// AI提报按钮样式
.ai-tech-btn {
  position: relative;
  overflow: hidden;
  border: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  border-radius: 8px;

  &:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  }

  &:active {
    color: white;
    transform: translateY(0);
    box-shadow: 0 3px 10px rgba(102, 126, 234, 0.5);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%);
    transition: left 0.8s;
  }

  &:hover::before {
    left: 100%;
  }

  .ai-btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .ai-btn-text {
    font-weight: 500;
    letter-spacing: 0.5px;
  }

  .ai-btn-icon {
    display: flex;
    align-items: center;
    animation: pulse-glow 2s infinite;

    svg {
      filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.5));
    }
  }

  @keyframes pulse-glow {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.8;
      filter: brightness(1.3);
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  :deep(.ant-btn-loading-icon) {
    color: white;
  }
}

</style>
