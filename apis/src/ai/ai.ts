// 在 ai.ts 文件中添加以下内容

import {eventGet} from '../eventRequest'
import {IAgentDTO,IAgentExecuteRequest} from "@haierbusiness-front/common-libs/src/ai";

// 新增AI助手相关API
export const aiApi = {
    // 执行AI助手对话（使用EventSource）
    executeAgent: (params: IAgentExecuteRequest, onMessage: (data: IAgentDTO) => void, onClose?: () => void, onError?: (error: any) => void) => {
        return eventGet<IAgentDTO>({
            url: 'hb/ai/api/agent/hpp/execute/flux',
            params: {
                message: params.message,
                chatId: params.chatId,
                enableMessageCheck: params.enableMessageCheck || false
            },
            onMessage,
            onClose,
            onError
        });
    },
}
