<script setup lang="ts">
import { showFailToast, But<PERSON> as VanButt<PERSON>, showDialog } from 'vant';
import 'vant/es/toast/style'
import 'vant/es/button/style'
import 'vant/es/dialog/style'

import finishIcon from '@/assets/image/finish.png';
import runningIcon from '@/assets/image/running.png';
import revokeIcon from '@/assets/image/revoke.png';
import rejectIcon from '@/assets/image/reject.png';
import logo from '@/assets/image/logo.png';
import { Tag as hTag, Avatar as hAvatar, Step as hStep,  Steps as hSteps, Table as hTable, Space as hSpace, Button as hButton, Col as hCol, Result as hResult, Row as hRow, TabPane as hTabPane, 
  Tabs as hTabs, message, Tooltip as hTooltip, Modal as hModal, Input as hInput, Textarea as hTextarea } from 'ant-design-vue';
import { CheckCircleOutlined, MinusCircleOutlined, StopOutlined, DownOutlined, CaretUpOutlined, CaretDownOutlined, UpOutlined } from '@ant-design/icons-vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { processApi } from '@haierbusiness-front/apis';
import { computed, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { ProcessStepStateConstant, ProcessStateConstant, IProcessRecordStep, ProcessStepOperatorStateConstant, ProcessNotificationMethodConstant, IProcessRecordStepOperator, ProcessRecordStepConstant, HeaderConstant } from '@haierbusiness-front/common-libs';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil'

const communicationUrl = import.meta.env.VITE_BUSINESS_COMMUNICATION_URL

const store = applicationStore()
const { loginUser } = storeToRefs(store)

const avatarName = computed(() => {
  if (loginUser?.value?.nickName) {
    const nickName = loginUser.value.nickName
    if (nickName.length === 2) {
      return nickName
    } else if (nickName.length > 2 && nickName.length <= 5) {
      return nickName.substring(nickName.length - 2, nickName.length)
    } else {
      return nickName.substring(0, 1).toUpperCase()
    }
  } else {
    return "李白"
  }
});

const isCurrentUser = computed(() => {
  if (loginUser?.value) {
    return (loginUser?.value?.username === detailsApiData.value?.processRecord?.applicantUser) && (detailsApiData?.value?.processRecord?.state == ProcessStateConstant.APPROVAL.type)
  }
  else
    return false
})

const result = ref();
const todoId = computed(
  () => {
    const urlSearchParams = new URLSearchParams(window.location.search);
    const tId = urlSearchParams.get("todoId");
    const code = urlSearchParams.get("code");
    if (tId || code) {
      if (tId) {
        return parseInt(tId);
      }
    }
    else {
      message.error("待办不存在!")
    }
  }
)

const code = computed(
  () => {
    const urlSearchParams = new URLSearchParams(window.location.search);
    const code = urlSearchParams.get("code");
    if (code) {
      return code;
    }
  }
)

const remark = ref("");

const {
  data: executeApiData,
  run: executeApiRun,
  loading: executeApiLoading,
} = useRequest(
  processApi.execute,
  {
    onSuccess: () => {
      detailsApiRun({ todoId: todoId.value, code: code.value })
    }
  }
);

const {
  data: revokeData,
  run: revokeApiRun,
  loading: revokeApiLoading,
} = useRequest(
  processApi.revoke,
  {
    onSuccess: () => {
      detailsApiRun({ todoId: todoId.value, code: code.value })
    }
  }
);

// 短信重发
const {
  data: smsResendData,
  run: smsResend,
  loading: smsResendLoading,
} = useRequest(
  processApi.reSend,
  {
    onSuccess: () => {
      // detailsApiRun({ todoId: todoId.value, code: code.value })
      message.success('审批短信重新发送成功')
    }
  }
);

// 获取日志
const {
  data: logData,
  run: logApiRun,
  loading: logApiLoading,
} = useRequest(processApi.logs);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: logData.value?.total,
  current: logData.value?.pageNum,
  pageSize: logData.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const dataSource = computed(() => {
  return logData.value?.records || []
});

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  logApiRun({
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const columns: ColumnType[] = [
{
    title: '步骤描述',
    dataIndex: 'stepName',
    width: '240px',
    align: 'center',
    ellipsis: true,
    fixed: 'left',
  },
  {
    title: '详细内容',
    dataIndex: 'content',
    width: '360px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '工号',
    dataIndex: 'createBy',
    width: '120px',
    align: 'center',
    ellipsis: true
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '120px',
    align: 'center',
    ellipsis: true
  }
]

const todoMethod = ref<any>([])

const {
  data: detailsApiData,
  run: detailsApiRun,
  loading: detailsApiLoading,
} = useRequest(
  processApi.details, {
  defaultParams: [
    {
      todoId: todoId.value,
      code: code.value
    }
  ],
  onSuccess: () => {
    logApiRun({ recordCode: detailsApiData.value?.processRecord?.code, pageNum: 1, pageSize: 10 })
    todoMethod.value = computedMethod(detailsApiData.value?.processRecord?.todoMethod)
    console.log(todoMethod.value,'todoMethod.value')
  },
  manual: false
}
);

const current = ref<IProcessRecordStep>({})
const currentStep = computed(
  () => {
    let index = 0;
    if (detailsApiData.value?.processRecord?.steps) {
      for (const i of detailsApiData.value?.processRecord?.steps) {
        if(i.result == 2 || i.result == 5) {
          current.value = i;
          return index;
        }
        if (i.state == ProcessStepStateConstant.APPROVAL.type) {
          current.value = i;
          return index;
        }
        index++;
      }
      return index;
    }
  }
);

const currentApprove = computed(
  () => {
    if (current?.value?.operators) {
      for (let i of current?.value?.operators) {
        if (i.approverCode === loginUser.value?.username && i.state == null) {
          return true;
        }
      }
      return false
    }
    return false
  })

// 0：只显示title  1：显示流程相关节点  2： 显示日志
const fold = ref(1)

const src = computed(() => {
  return detailsApiData.value?.processRecord?.orderDetailsUrl
})

const scrollHeight = ref(document.body.scrollHeight)

const foldFooter = () => {
  if (fold.value === 2) {
    fold.value = 0
  } else {
    fold.value +=1
  }
}

const content = computed(() => {
  if(fold.value === 0) {
    return 'transform: translateY(460px)'
  } else if(fold.value === 1) {
    return 'transform: translateY(310px)'
  } else {
    return ''
  }
})

const contentStyle = computed(() => {
  if(fold.value === 0) {
    return { height: 'calc(100% - 31px)' }
  } else {
    return { height: 'calc(100% - 244px)' }
  }
})


const computedMethod = (num: number | undefined)=> {
    if(!num) {
        return
    }

    const list = ProcessNotificationMethodConstant.toNumberArray()
    console.log(list)
    // method
    const array: number[] = []
    list.map(item => {
        if((num & item!) != 0) {
            array.push(item!)
        }
    })
    return array
}

const approverName = (operators: IProcessRecordStepOperator[]) => {
  console.log(operators,"-------------------")
  // applicantUser
  const index = operators.findIndex(o => o.applicantUser === loginUser.value?.username)
  if(index >= 0) {
    let name = operators[index].approverName
    if(operators.length > 1) {
      name += '等其他' + (operators.length - 1) + '人'
    }
    return name
  } else {
    let name = operators[0].approverName
    if(operators.length > 1) {
      name += '等其他' + (operators.length - 1) + '人'
    }
    return name
  } 
}

const getResult = (name: string,  result: number | undefined, time: string) => {
  if(result || time) {
    name += ':'
    if(time) {
      name += time
    }
    if(result) {
      const resultStr = ProcessRecordStepConstant.ofType(result)
      if (resultStr) {
        name += '，' + resultStr.name
      }
    }
  }
  return name
}

const visible = ref<boolean>(false);

const showModal = () => {
  visible.value = true;
};

const handleOk = () => {
  debugger
  if(!remark.value) {
    message.error('驳回原因不能为空！')
    return
  }
  else {
    result.value = 2
    executeApiRun({ todoId: todoId.value,processCode:detailsApiData.value?.processRecord?.code, result: 2, remark: remark.value })
  }
  visible.value = false;
}

const addParams = (url: string) => {
  if (!url) {
    return ''
  }
  const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false)
  if(url.indexOf('?') > -1) {
    return '&hb-token=' + token
  } else {
    return '?hb-token=' + token
  }
}

const upDown = ref('down')

const setUpDown = (status: 'up' | 'down') => {
  upDown.value = status
}

const pass = () => {
  // 添加判断是否是会展账单页面，如果是的话，调用检查账单页面中的附件查看情况
  if(src.value && src.value.indexOf('/haiermice/mice/manage/tracking/billdetail') > -1) {
    const element = document.getElementById('details_iframe') as HTMLIFrameElement
    element?.contentWindow?.postMessage("process_checkFile", communicationUrl)
  } else {
    result.value = 1; 
    executeApiRun({ todoId: todoId.value,processCode:detailsApiData.value?.processRecord?.code, result: 1, remark: remark.value })
  }
}

const handleMessage = (event: any) => {
  if (event.origin === communicationUrl) {
    if (event.data.type === 'checkSuccess') {
      result.value = 1; 
      executeApiRun({ todoId: todoId.value,processCode:detailsApiData.value?.processRecord?.code, result: 1, remark: remark.value })
    }
  }
}

window.addEventListener('message', handleMessage, false)

</script>

<template>
  <div :style="{ height: scrollHeight + 'px', backgroundColor: '#fffff' }">
    <h-row class="header">
      <h-col :span="8" :offset="1">
        <img :src="logo" class="logo-img" />
      </h-col>
      <h-col :span="15" style="text-align: right;">
        <h-avatar :title="loginUser?.username" size="large"
          style="color: #f56a00; background-color: #fde3cf;user-select: none;margin-top: 4px;margin-right: 20px;">{{
            avatarName }}</h-avatar>
      </h-col>
    </h-row>
    <h-row class="details_iframe_body" :style="contentStyle">
      <iframe v-if="src" :src="src + addParams(src ?? '')" frameborder="0" class="details_iframe" id="details_iframe"></iframe>
    </h-row>
  </div>
  <div class="drawer drawer-bottom" :class="{'drawer-open': fold === 2 }">
    <div class="mask" :class="{ 'mask-show' : fold === 2 }"></div>
    <div class="drawer-content" style="height: 500px" :style="content" >
      <h-row style="height: 190px;">
        <h-row style="width: 100%;">
          <h-col :span="17">
            <div style="margin-left: 10px;margin-top: 10px;"  class="ellipsis">
              <div style="margin-left: 10px;margin-right: 20px;vertical-align: super; display:inline-block;"
                @click="foldFooter">
                <DownOutlined v-if="fold === 2" />
                <UpOutlined v-else />
                
              </div>
              <span style="font-weight: 600;font-size: 14px;vertical-align: super;">{{ detailsApiData?.processRecord?.title
              }}</span>
              <span style="margin-left: 10px;margin-right: 2%; font-size: 14px;vertical-align: super;">{{
                detailsApiData?.processRecord?.code
              }}</span>
              <span style="margin-left: 2px;margin-right: 2%; font-size: 14px;vertical-align: super;">业务单号：{{
                detailsApiData?.processRecord?.businessCode
              }}</span>
              <span style="margin-left: 2px;margin-right: 2%; font-size: 14px;vertical-align: super;">发起人: {{ detailsApiData?.processRecord?.applicantName }}</span>

              <template v-if="detailsApiData?.processRecord?.description">
                <span style="font-weight: 600;font-size: 14px;vertical-align: super;">申请原因：</span>
                <span style="margin-left: 10px;margin-right: 40px; font-size: 14px;vertical-align: super;" :title="detailsApiData?.processRecord?.description"> {{
                  detailsApiData?.processRecord?.description
                }}</span>
              </template>
            </div>
          </h-col>
          <h-col :span="6">
            <div v-if="detailsApiData?.processRecord?.rejectedRemark" class="rejected" :title="detailsApiData?.processRecord?.rejectedRemark">
              <span style="font-weight: 600;font-size: 14px;">驳回原因：</span> {{ detailsApiData?.processRecord?.rejectedRemark }}
            </div>
          </h-col>
          <h-col
            :span="detailsApiData?.processRecord?.steps?.length && detailsApiData?.processRecord?.steps?.length <= 3 ? 18 : 20"
            :offset="detailsApiData?.processRecord?.steps?.length && detailsApiData?.processRecord?.steps?.length <= 3 ? 3 : 1" style="align-items:center; display:flex;">
            <h-steps :current="currentStep">
              <h-step v-for="(i,index) in detailsApiData?.processRecord?.steps" :key="i.id">
                <template #icon v-if="i.result == 2"><StopOutlined style="color: red;" /></template>
                <template #title>
                  <div style="display: flex; align-items: center;">
                    <span style="font-weight: 600; margin-right: 4px;">{{ i.psName }}</span>
                    <h-tag v-if="index == currentStep"  color="blue">当前节点</h-tag>
                  </div>
                  
                </template>
                <template #description v-if="i?.operators?.length">
                  <h-tooltip color="#ffffff">
                      <template #title>
                        <div class="tooltip">
                          <template v-for="o in i.operators" :key="o.id">
                            <div :title="o.approverCode + '：' + o.approverRemark" style="color: rgba(0, 0, 0, 0.85); padding: 10px;">{{o.approveUserType==1?'':'('+o.masterApproverName+'的代审)'}}{{ getResult(o.approverName || '',i.result,  o.completeTime || '') }}
                              <CheckCircleOutlined v-if="o.state === ProcessStepOperatorStateConstant.PASS.type"
                                style="color: green;" />
                              <StopOutlined v-if="o.state === ProcessStepOperatorStateConstant.REJECT.type" style="color: red;" />
                              <MinusCircleOutlined v-if="o.state === ProcessStepOperatorStateConstant.ABSTENTION.type" />
                            </div>
                          </template>
                        </div>
                      </template>
                      <div style="color: rgba(0, 0, 0, 0.45);" @mousemove="setUpDown('up')" @mouseleave="setUpDown('down')">
                        <CaretUpOutlined v-if="upDown === 'up'" />
                        <CaretDownOutlined v-if="upDown === 'down'" />
                        <!-- {{ i.operators[0].approverName + (i.operators.length > 1 ? ' ...' : '') }} -->
                        {{ approverName(i.operators) }} 
                        <!-- {{ i.operators[0].approverName + '，' + i.operators[0].approverName + '，' + i.operators[0].approverName }} -->
                      </div>
                    </h-tooltip>
                </template>
              </h-step>
            </h-steps>
          </h-col>
          <h-col :span="2" :offset="1">
            <template v-if="detailsApiData?.processRecord?.state == ProcessStateConstant.APPROVAL.type">
              <template v-if="currentApprove">
                <!-- && !code -->
              <!-- <template v-if="true"> -->
                <h-button style="margin: 4px;" type="primary"
                  @click="pass"
                  :loading="executeApiLoading && result === 1">通过</h-button><br/>
                  <h-button style="margin: 4px;" type="primary"
                  @click="showModal"
                  :loading="executeApiLoading && result === 2" danger>驳回</h-button>
              </template>
              <template v-else>
                <template v-if="isCurrentUser && code">
                  <h-button 
                    v-if="todoMethod && todoMethod.indexOf(4) > -1"
                    @click="smsResend({processCode:code})" type="primary"
                    :loading="smsResendLoading">短信重发</h-button>
                  <h-button style="margin: 4px 0;width: 88px;" type="primary"
                    @click="revokeApiRun(code)"
                    :loading="revokeApiLoading" danger> 撤 回 </h-button>
                </template>
                <template v-else>
                  <img :src="runningIcon" class="state-icon-img" />
                </template>
              </template>
            </template>
            <template v-else-if="detailsApiData?.processRecord?.state == ProcessStateConstant.REJECT.type">
              <img :src="rejectIcon" class="state-icon-img" />
            </template>
            <template v-else-if="detailsApiData?.processRecord?.state == ProcessStateConstant.REVOKE.type">
              <img :src="revokeIcon" class="state-icon-img" />
            </template>
            <template v-else>
              <img :src="finishIcon" class="state-icon-img" />
            </template>
          </h-col>
        </h-row>
      </h-row>
      <h-row class="log">
        <h-col :offset="1" :span="22" style="font-weight: bold;font-size: 18px;">
          日志：
        </h-col>
        <h-col :offset="1" :span="22">
          <h-table :columns="columns" :row-key="record => record.id" :data-source="dataSource"
            :pagination="pagination" :scroll="{ y: 550 }" :loading="logApiLoading" @change="handleTableChange($event as any)">
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'state'">
                {{ record.state === 1 ? '成功' : '失败' }}
              </template>
            </template>
          </h-table>
        </h-col>
      </h-row>
    </div>
  </div>

  <h-modal v-model:open="visible" title="填写驳回原因" @ok="handleOk" :confirmLoading="executeApiLoading && result === 2">
      <h-row>
        <h-col :span="6" class="reason">
          <span style="font-weight: 600;font-size: 14px;vertical-align: super;">驳回原因：</span>
        </h-col>
        <h-col :span="16">
          <h-textarea v-model:value="remark" />
        </h-col>
      </h-row>
  </h-modal>
  
</template>

<style scoped lang="less">

.reason {
  display: flex;
  height: auto;
  align-items: center;
  flex-direction: row-reverse;
}

.state-icon-img {
  width: 80px;
  height: 80px;
  transform: rotate(53deg)
}

.header {
  height: 50px;
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
  box-shadow: 0px 20px 80px 0px rgb(0 0 0 / 30%);

  .logo-img {
    margin-top: 12px;
    height: 20px;
  }
}

.details_iframe_body {
  height: calc(100% - 244px);
  background-color: #fff;
  margin: 2px 0 2px 0;

  .details_iframe {
    width: 100%;
  }
}

.footer-close {
  height: 190px;
}

.footer-fold {
  position: relative;
  height: 190px;
  border-top-right-radius: 12px;
  border-top-left-radius: 12px;
  box-shadow: 0px 20px 50px 0px rgb(0 0 0 / 10%);
}

.drawer {
  position: fixed;
  z-index: 1000;
  width: 0%;
  height: 100%;
  transition: width 0s ease .3s,height 0s ease .3s;
}

.drawer-bottom {
  bottom: 0;
  left: 0;
  width: 100%;
  height: 190px;
}

.drawer-open {
  height: 100%;
  transition: transform .3s cubic-bezier(.23,1,.32,1);
}

.mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
  background-color: #00000073;
  opacity: 0;
  transition: opacity .3s linear,height 0s ease .3s;
  pointer-events: none;
}

.mask-show {
  height: 100%;
  opacity: 1;
  transition: none;
  animation: bwdvDrawerFadeIn .3s cubic-bezier(.23,1,.32,1);
  pointer-events: auto;
}

.drawer-content {
  box-shadow: 0 -6px 16px -8px #00000014, 0 -9px 28px #0000000d, 0 -12px 48px 16px #00000008;
  bottom: 0;
  width: 100%;
  position: absolute;
  transition: transform .3s cubic-bezier(.23,1,.32,1),box-shadow .3s cubic-bezier(.23,1,.32,1);
  background-color: #ffffff;
}

.log {
  height: 310px;
  overflow: auto;
}

.rejected {
  margin-top: 10px;
  white-space:nowrap;
	overflow:hidden;
	text-overflow:ellipsis;
}

.ellipsis {
  white-space:nowrap;
	overflow:hidden;
	text-overflow:ellipsis;
}

:root:root {
  --van-button-primary-background: #0073E5;
  --van-radio-checked-icon-color: #0073E5;
  --van-password-input-background: #F2F2F2;
}
</style>

<style>
.tooltip {
  padding: 10px;
}

.ant-tooltip {
  max-width: 400px !important;
}
</style>
