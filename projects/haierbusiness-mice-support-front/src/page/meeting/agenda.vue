<script lang="ts" setup>
import {
  message,
  Modal,
} from 'ant-design-vue';
import { ExclamationCircleOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { computed, createVNode, h, nextTick, onMounted, reactive, ref } from 'vue';
import { usePagination } from 'vue-request';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { meetingAgendaApi, meetingAttendeeApi, userApi } from '@haierbusiness-front/apis';
import { IMeetingAgenda, IMeetingDetails } from '@haierbusiness-front/common-libs';
import { useRoute, useRouter } from 'vue-router';
import Actions from '@haierbusiness-front/components/actions/Actions.vue';
import dayjs, { Dayjs } from 'dayjs';
import type { MenuInfo, MenuItemType } from 'ant-design-vue/lib/menu/src/interface'
const route = useRoute();
const router = useRouter();

const id = route.query.id
//会议详情
const meetingDetails = ref<IMeetingDetails>()
//议程数据
const meetingAgenda = ref<IMeetingAgenda>({})

//弹框可见
const Visible = ref(false)
const importModel = ref(false)
const title = ref('')

const formRef = ref();

const confirmLoading = ref<boolean>(false);

const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(meetingAgendaApi.list);

const dataSource = computed(() => data.value?.records || []);

const handleAdd = () => {
  title.value = '新增议程'
  meetingAgenda.value = {}
  nextTick(() => {
    meetingAgenda.value = {}
    formRef.value?.resetFields()
  })
  Visible.value = true
}

const handleImport = () => {
  ConferenceDocuments.value = null
  importModel.value = true
}
//导出文件
const handleExport = async () => {
  if (!meetingDetails.value?.miceName) {
    message.error('会议信息获取失败，请刷新页面重试')
    return
  }

  uploadLoading.value = true
  try {
    const params = {
      miceInfoId: Number(id) || 0,
      miceInfoName: meetingDetails.value.miceName
    }
    await meetingAgendaApi.export(params)
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  } finally {
    uploadLoading.value = false
  }
}
//导入文件
let ConferenceDocuments = ref<File | null>();
const uploadLoading = ref<boolean>(false);
const fileInputRef = ref<HTMLInputElement | null>(null);

// 触发文件选择
const triggerFileSelect = () => {
  if (fileInputRef.value) {
    fileInputRef.value.click();
  }
};

// 选择文件
const handleFileChange = (e: Event) => {
  const input = e.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    ConferenceDocuments.value = input.files[0];
  }
};

const handleFileImport = () => {
  if (!ConferenceDocuments.value) {
    message.error('请先选择文件');
    return;
  }

  uploadLoading.value = true;

  const formData = new FormData();
  formData.append("file", ConferenceDocuments.value);
  formData.append("miceInfoId", '1');

  meetingAgendaApi.import(formData as any)
    .then(res => {
      message.success('导入成功');
      importModel.value = false;
      listApiRun({
        miceId: id,
        pageNum: 1,
        pageSize: 50
      })
    })
    .catch((error) => {
      console.error('导入失败:', error);
      message.error('导入失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
}

const currentRouter = ref()



const meetingDetail = async () => {
  const meetingId = Number(id)
  if (!meetingId) {
    message.error('会议ID缺失')
    return
  }
  try {
    const response = await meetingAttendeeApi.details(meetingId)
    if (response) {
      meetingDetails.value = response
    }
  } catch (error) {
    console.error('获取会议详情失败:', error)
    message.error('获取会议详情失败')
  }
}


const agendaDetail = async (id: number) => {
  try {
    const response = await meetingAgendaApi.details(id)
    if (response) {
      meetingAgenda.value = response
      // console.log(meetingAgenda.value, "meetingAgenda.value");
    }
    meetingAgenda.value.agendaDate = dayjs(response.agendaDate)
    meetingAgenda.value.startTime = dayjs(response.startTime)
    meetingAgenda.value.endTime = dayjs(response.endTime)
  } catch (error) {
    console.error('获取议程详情失败:', error)
    message.error('获取议程详情失败')
  }
}

onMounted(async () => {
  currentRouter.value = await router
  // console.log(router, "router");
  await meetingDetail()
  const meetingId = Number(id)
  if (meetingId) {
    listApiRun({
      miceId: meetingId,
      pageNum: 1,
      pageSize: 50
    })
  }
})

const columns: ColumnType[] = [
  {
    title: '序号',
    dataIndex: 'index',
    width: '80px',
    // sorter: (a, b) => a.index - b.index,
    align: 'center',
    customRender: ({ index }) => index + 1
  },
  {
    title: '日期',
    dataIndex: 'agendaDate',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '地点',
    dataIndex: 'address',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '开始时间',
    dataIndex: 'startTime',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '会议内容',
    dataIndex: 'subject',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center',
  },

];

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: false,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'right' },
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
) => {
  loading.value = true
  const meetingId = Number(id)
  if (!meetingId) return
  try {
    listApiRun({
      miceInfoId: meetingId,
      pageNum: pag.current,
      pageSize: pag.pageSize
    })
    loading.value = false
  } catch (error) {

  } finally {
    loading.value = false
  }


};

const rules = {
  agendaDate: [
    { required: true, message: '请选择', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入', trigger: 'blur' }
  ],
  startTime: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  endTime: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  subject: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  speaker: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
};
//确认
const handleOk = () => {
  let start: Dayjs | string = dayjs(meetingAgenda.value.startTime)
  let end: Dayjs | string = dayjs(meetingAgenda.value.endTime)
  let agendaDate: Dayjs | string = dayjs(meetingAgenda.value.agendaDate)
  // console.log(start,end,agendaDate);

  if (!start.isSame(agendaDate, 'day')) {
    message.error('日期时间应和开始时间一样')
    return
  }

  if (start.isAfter(end)) {
    message.error('结束时间不能早于开始时间')
    return
  }
  confirmLoading.value = true;
  formRef.value
    .validate()
    .then(() => {
      const miceId = Number(id)
      if (title.value == '新增议程') {
        const params = {
          miceInfoId: miceId,
          agendaDate: dayjs(meetingAgenda.value.agendaDate).format('YYYY-MM-DD'),
          address: meetingAgenda.value.address,
          startTime: dayjs(meetingAgenda.value.startTime).format('YYYY-MM-DD 00:00:00'),
          endTime: dayjs(meetingAgenda.value.endTime).format('YYYY-MM-DD 23:59:59'),
          subject: meetingAgenda.value.subject,
          speaker: meetingAgenda.value.speaker,
          sort: Number(dataSource.value[0].sort) + 1
        }
        meetingAgendaApi.save(params)
          .then(() => {
            // 成功处理
            confirmLoading.value = false;
            message.success('新增成功')
            Visible.value = false
            listApiRun({
              miceId: miceId,
              pageNum: 1,
              pageSize: 50
            })
          })
          .catch((error) => {
            // 错误处理
            confirmLoading.value = false;
            message.success('新增失败')
          });
      } else if (title.value == '编辑议程') {
        const params = {
          id: meetingAgenda.value.id,
          miceInfoId: meetingAgenda.value.miceInfoId,
          agendaDate: dayjs(meetingAgenda.value.agendaDate).format('YYYY-MM-DD '),
          address: meetingAgenda.value.address,
          startTime: dayjs(meetingAgenda.value.startTime).format('YYYY-MM-DD 00:00:00'),
          endTime: dayjs(meetingAgenda.value.endTime).format('YYYY-MM-DD 23:59:59'),
          subject: meetingAgenda.value.subject,
          speaker: meetingAgenda.value.speaker,
          sort: meetingAgenda.value.sort,
        }
        meetingAgendaApi.edit(params)
          .then(() => {
            // 成功处理
            confirmLoading.value = false;
            message.success('编辑成功')
            Visible.value = false
            listApiRun({
              miceId: miceId,
              pageNum: 1,
              pageSize: 50
            })
          })
          .catch((error) => {
            // 错误处理
            confirmLoading.value = false;
            message.success('编辑失败')
          });
      }

    })
    .catch((errors: any) => {
      console.error('表单验证失败:', errors);
      confirmLoading.value = false;
    });
};

const handleDelete = (res: number) => {
  const miceId = Number(id)
  Modal.confirm({
    title: '删除提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', { style: 'color:red;' }, '确定要删除'),
    onOk() {
      meetingAgendaApi.remove(res)
        .then(() => {
          message.success('删除成功')
          listApiRun({
            miceId: miceId,
            pageNum: 1,
            pageSize: 50
          })
        })
    },
    onCancel() {
      // console.log('Cancel');
    },
  });

}
//编辑
const handleEdit = async (record: any) => {
  await agendaDetail(record.id)
  title.value = '编辑议程'
  Visible.value = true
}

//下移
const moveDown = async (record: IMeetingAgenda,index:number) => {
    const paramsDown = {
      id: record.id,
      sort: dataSource.value[index + 1].sort
    }
    const paramsUp = {
      id: dataSource.value[index + 1].id,
      sort: record.sort
    }
    // console.log(paramsDown, paramsUp);

    await meetingAgendaApi.moveUp(paramsDown)
    await meetingAgendaApi.moveUp(paramsUp)
    await listApiRun({
      miceId: Number(id),
      pageNum: 1,
      pageSize: 50
    })
}

//上移
const moveUp = async (record: IMeetingAgenda,index:number) => {

    const paramsUp = {
      id: record.id,
      sort: dataSource.value[index - 1].sort
    }
    const paramsDown = {
      id: dataSource.value[index - 1].id,
      sort: record.sort
    }
    // console.log(paramsDown, paramsUp);

    await meetingAgendaApi.moveUp(paramsDown)
    await meetingAgendaApi.moveUp(paramsUp)
    await listApiRun({
      miceId: Number(id),
      pageNum: 1,
      pageSize: 50
    })
}

// 更多按钮数组
const computedOptions = (index: number, record: IMeetingAgenda) => {
  let options: MenuItemType[] = []
  if (index != 0) {
    options.push(
      {
        key: '1',
        label: '上移',
      },
    )
  }
  if (index != dataSource.value.length - 1) {
    options.push(
      {
        key: '2',
        label: '下移',
      },
    )
  }
  return options
}

// 点击数组中的菜单项
const handleMenuClick = (record: IMeetingAgenda, e: MenuInfo, index: number) => {
  if (e.key === '1') {
    moveUp(record,index)
  }
  if (e.key === '2') {
    moveDown(record,index)
  }
}


</script>

<template>
  <div class="container">
    <div class="content">
      <div class="nav">
        会议首页/{{ router.currentRoute?.value.meta.title }}
      </div>
      <div class="main">
        <div class="main-top">
          <div class="top-title">
            {{ router.currentRoute?.value.meta.title }}
          </div>
          <ul class="top-right">
            <li class="" style="margin-right: 10px;">
              <a-button type="primary" @click="handleAdd">添加</a-button>
            </li>
            <li>
              <a-button @click="handleImport" style="margin-right: 10px;">导入</a-button>
            </li>
            <li>
              <a-button @click="handleExport" style="margin-right: 10px;" :loading="uploadLoading">导出</a-button>
            </li>
          </ul>
        </div>
        <div class="center-main">
          <div class="center-middle">
            <a-table :rowKey="(record: any) => record.id" :columns="columns" :data-source="dataSource"
              :loading="loading" :pagination="pagination" @change="handleTableChange($event as any)">
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.dataIndex === '_operator'">
                  <a-button type="link" @click="handleEdit(record)">编辑</a-button>
                  <a-button type="link" @click="handleDelete(record.id)">删除</a-button>
                  <!-- <a-button v-if="index != Number(dataSource.length) - 1" type="link" @click="moveDown(record)">下移</a-button>
                  <a-button v-if="index != 0" type="link" @click="moveUp(record)">上移</a-button> -->
                  <Actions :menu-options="computedOptions(index, record)"
                    :on-menu-click="(key) => handleMenuClick(record, key, index)"></Actions>
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </div>
    <Modal v-model:open="Visible" :title="title" width="800px" :confirm-loading="confirmLoading" @ok="handleOk">
      <a-form ref="formRef" :rules="rules" :model="meetingAgenda" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }"
        hideRequiredMark="false">
        <a-form-item label="日期：" name="agendaDate">
          <a-date-picker v-model:value="meetingAgenda.agendaDate" style="width: 100%;" />
        </a-form-item>
        <a-form-item label="地点：" name="address">
          <a-input id="address" v-model:value="meetingAgenda.address" allow-clear placeholder="请输入地点" />
        </a-form-item>
        <a-form-item label="开始时间：" name="startTime">
          <a-date-picker v-model:value="meetingAgenda.startTime" style="width: 100%;" />
        </a-form-item>
        <a-form-item label="结束时间：" name="endTime">
          <a-date-picker v-model:value="meetingAgenda.endTime" style="width: 100%;" />
        </a-form-item>
        <a-form-item label="会议内容：" name="subject">
          <a-input id="subject" v-model:value="meetingAgenda.subject" allow-clear placeholder="请输入地点" />
        </a-form-item>
        <a-form-item label="发言人：" name="speaker	">
          <a-input id="speaker" v-model:value="meetingAgenda.speaker" allow-clear placeholder="请输入地点" />
        </a-form-item>


      </a-form>
    </Modal>

    <Modal v-model:open="importModel" title="导入参会人" width="35%" :confirm-loading="confirmLoading">
      <template #footer>
        <a-button key="back" @click="importModel = false">取消</a-button>
        <a-button key="submit" type="primary" @click="handleFileImport">导入</a-button>
      </template>
      <a-row :gutter="[16, 16]" style="margin-top: 20px;">
        <a-col :span="6" style="text-align: right;line-height: 32px;">
          上传文件：
        </a-col>
        <a-col :span="10">
          <input ref="fileInputRef" type="file" style="display: none;" accept=".xlsx,.xls,.csv"
            @change="handleFileChange" />
          <a-button type="primary" @click="triggerFileSelect" style="margin-right: 8px;">
            <UploadOutlined /> 选择文件
          </a-button>
        </a-col>
        <a-col :span="8" style="line-height: 32px;">
          <a-button type="link">下载导入模板</a-button>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="15" style="line-height: 32px;text-align: right;margin-top: 0px;">
          <span v-if="ConferenceDocuments">{{ ConferenceDocuments.name }}</span>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24" style="line-height: 32px;text-align: center;margin-top: 20px;">
          <img src="../../assets/image/orderList/warn.png" alt=""
            style="width: 17px;height: 17px;vertical-align: middle;">导入新数据后将覆盖<span
            style="color: red;">来源为录入</span>的全部数据
        </a-col>

      </a-row>
    </Modal>
  </div>
</template>
<style lang="scss" scoped>
* {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
}

ul,
p {
  margin-bottom: 0;
}

ul li {
  list-style: none;
}

:deep(.ant-select) {
  min-width: 100px;
}

.container {
  height: auto;
  width: 100%;
  background: #F6F7F9;
}

.content {
  width: 1280px;
  height: auto;
  margin: 0 auto;

  .main {
    width: 100%;
    background-color: #fff;

    .main-top {
      width: 100%;
      border: 1px solid #E5E6EB;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 18px 24px;

      .top-title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 20px;
        color: #1D2129;
        line-height: 28px;
        text-align: left;
        font-style: normal;
      }

      .top-right {
        display: flex;
        align-items: center;
        margin-bottom: 0 !important;

        .copy {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #1868DB;
          line-height: 20px;
          text-align: right;
          font-style: normal;

          img {
            width: 16px;
            height: 16px;
          }
        }
      }

    }

    .center-main {
      padding: 20px 24px;

      .center-top {
        display: flex;

        span {
          margin-left: 2px;
        }
      }

      .top-left {
        width: 109px;
        height: 32px;
        background: #1868DB;
        border-radius: 4px;
        line-height: 32px;
        text-align: center;
        font-size: 14px;
        color: #fff;
        margin-right: 12px;
      }

      .top-center {
        width: 185px;
        height: 32px;
        background: #F2F3F5;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        color: #1D2129;
        line-height: 22px;
        text-align: center;
        font-style: normal;

        .line {
          width: 1px;
          height: 18px;
          background: #E7E7E7;
        }
      }

      .center-middle {
        margin-top: 20px;
      }
    }
  }
}

.nav {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #86909C;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  padding: 16px 0px;
}
</style>