<script lang="ts" setup>
import {
  message,
  Modal,
} from 'ant-design-vue';
import { ExclamationCircleOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { computed, createVNode, h, nextTick, onMounted, reactive, ref } from 'vue';
import { usePagination } from 'vue-request';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import type { Rule } from 'ant-design-vue/lib/form/interface';
import { meetingAttendeeApi, meetingGuestApi, userApi, fileApi } from '@haierbusiness-front/apis';
import { IMeetingAgenda, IMeetingDetails, AttendeeType, ATTENDEE_TYPE_MAP, IMeetingGuest } from '@haierbusiness-front/common-libs';
import { useRoute, useRouter } from 'vue-router';
const route = useRoute();
const router = useRouter();

const id = Number(route.query.id)
//会议详情
const meetingDetails = ref<IMeetingDetails>()
//添加会议嘉宾
const meetingGuest = ref<IMeetingGuest>({})

//弹框可见
const Visible = ref(false)
const importModel = ref(false)

//是否可编辑
const isEditId = ref(0)
const editState = ref(false)

const formRef = ref();

const confirmLoading = ref<boolean>(false);

// 文件上传相关变量
const imageFileList = ref<any[]>([]);
const baseUrl = import.meta.env.VITE_BUSINESS_URL || '';

const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(meetingGuestApi.list);

const dataSource = computed(() => data.value?.records || []);

const handleAdd = () => {
  meetingGuest.value = {}
  imageFileList.value = []  // 重置文件列表
  nextTick(() => {
    formRef.value?.resetFields()
  })
  Visible.value = true
}

const handleImport = () => {
  ConferenceDocuments.value = null
  importModel.value = true
}
//导出文件
const handleExport = async () => {
  if (!meetingDetails.value?.miceName) {
    message.error('会议信息获取失败，请刷新页面重试')
    return
  }

  uploadLoading.value = true
  try {
    const params = {
      miceInfoId: Number(id) || 0,
      miceInfoName: meetingDetails.value.miceName
    }
    await meetingGuestApi.export(params)
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  } finally {
    uploadLoading.value = false
  }
}
//导入文件
let ConferenceDocuments = ref<File | null>();
const uploadLoading = ref<boolean>(false);
const fileInputRef = ref<HTMLInputElement | null>(null);

// 触发文件选择
const triggerFileSelect = () => {
  if (fileInputRef.value) {
    fileInputRef.value.click();
  }
};

// 选择文件
const handleFileChange = (e: Event) => {
  const input = e.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    ConferenceDocuments.value = input.files[0];
  }
};

const handleFileImport = () => {
  if (!ConferenceDocuments.value) {
    message.error('请先选择文件');
    return;
  }

  uploadLoading.value = true;

  const formData = new FormData();
  formData.append("file", ConferenceDocuments.value);
  formData.append("miceInfoId", '1');

  meetingGuestApi.import(formData as any)
    .then(res => {
      message.success('导入成功');
      importModel.value = false;
      listApiRun({
        miceId: id,
        pageNum: 1,
        pageSize: 50
      })
    })
    .catch((error) => {
      console.error('导入失败:', error);
      message.error('导入失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
}

const currentRouter = ref()



const meetingDetail = async () => {
  const meetingId = Number(id)
  if (!meetingId) {
    message.error('会议ID缺失')
    return
  }
  try {
    const response = await meetingAttendeeApi.details(meetingId)
    if (response) {
      meetingDetails.value = response
    }
  } catch (error) {
    console.error('获取会议详情失败:', error)
    message.error('获取会议详情失败')
  }
}

onMounted(async () => {
  currentRouter.value = await router
  listApiRun({
    miceId: id,
    pageNum: 1,
    pageSize: 50
  })
  await meetingDetail()

})

const columns: ColumnType[] = [
  {
    title: '序号',
    dataIndex: 'id',
    width: '80px',
    // sorter: (a, b) => a.id - b.id,
    align: 'center',
    customRender: ({ index }) => index + 1
  },
  {
    title: '嘉宾来源',
    dataIndex: 'guestSource',
    ellipsis: true,
    customRender: ({ text }) => {
      return ATTENDEE_TYPE_MAP[text] || '-';
    }
  },
  {
    title: '姓名',
    dataIndex: 'name',
    ellipsis: true,
  },
  {
    title: '所属单位',
    dataIndex: 'companyName',
    ellipsis: true,
  },
  {
    title: '照片',
    dataIndex: 'imageUrl',
    ellipsis: true,
    customRender: ({ text }) => {
      return h('a', {
        onClick: () => {
          if (text) {
            window.open(text, '_blank');
          }
        },
        style: { cursor: 'pointer', color: '#1890ff' }
      }, text ? '查看照片' : '-');
    }
  },
  {
    title: '嘉宾头衔',
    dataIndex: 'title',
    ellipsis: true,
  },
  {
    title: '嘉宾简介',
    dataIndex: 'description',
    ellipsis: true,
  },
  {
    title: '禁忌与偏好',
    dataIndex: 'specialRequest',
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    ellipsis: true,
  },
  {
    title: '排序',
    dataIndex: 'sort',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '280px',
    fixed: 'right',
    align: 'center',
  },

];

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: false,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'right' },
}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
) => {
  loading.value = true
  const meetingId = Number(id)
  if (!meetingId) return
  try {
    listApiRun({
      miceInfoId: meetingId,
      pageNum: pag.current,
      pageSize: pag.pageSize
    })
    loading.value = false
  } catch (error) {

  } finally {
    loading.value = false
  }


};

const rules: Record<string, Rule[]> = reactive({
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  guestSource: [
    { required: true, message: '请选择嘉宾来源', trigger: 'change' }
  ],
  companyId: [
    { required: true, message: '请选择所属单位', trigger: 'change' }
  ],
  // You can add more rules for other fields here
});



const handleOk = () => {
  console.log('========== 嘉宾信息 ==========');
  console.log('所有输入的值:', meetingGuest.value);

  // loading.value = true;
  formRef.value
    .validate()
    .then(() => {
      meetingGuest.value.miceInfoId = id

      // 根据是否有id判断是新增还是编辑
      const isEdit = !!meetingGuest.value.id
      if (!isEdit) {
        meetingGuest.value.sort = Number(dataSource.value[0].sort) + 1
      }
      const apiMethod = isEdit ? meetingGuestApi.edit : meetingGuestApi.save
      const successMsg = isEdit ? '编辑成功' : '新增成功'
      const errorMsg = isEdit ? '编辑失败' : '新增失败'

      apiMethod(meetingGuest.value)
        .then(() => {
          // 成功处理
          loading.value = false;
          message.success(successMsg)
          Visible.value = false
          listApiRun({
            miceId: id,
            pageNum: 1,
            pageSize: 50
          })
        })
        .catch((error) => {
          // 错误处理
          loading.value = false;
          message.error(errorMsg)
        });
    })
    .catch((errors: any) => {
      console.error('表单验证失败:', errors);
      loading.value = false;
    });
};


const handleEdit = async (record: any) => {
  nextTick(() => {
    formRef.value?.clearValidate()
  })
  // 直接使用表格中的数据进行回显
  meetingGuest.value = {
    id: record.id,
    guestSource: record.guestSource,
    name: record.name,
    companyId: record.companyId,
    companyName: record.companyName,
    imageUrl: record.imageUrl,
    title: record.title,
    description: record.description,
    specialRequest: record.specialRequest,
    remark: record.remark,
    sort: record.sort
  }

  // 如果有图片，设置图片列表
  if (record.imageUrl) {
    imageFileList.value = [{
      uid: '-1',
      name: '已上传图片',
      status: 'done',
      url: record.imageUrl,
      filePath: record.imageUrl
    }]
  } else {
    imageFileList.value = []
  }

  Visible.value = true
}

// 文件上传请求函数
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  console.log('开始上传文件:', options.file.name);

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;
      options.file.fileName = options.file.name;
      options.onProgress(100);
      options.onSuccess(it, options.file);

      console.log('文件上传成功:', options.file);

      // 更新表单中的imageUrl
      meetingGuest.value.imageUrl = options.file.filePath;

      // 确保文件被添加到列表中
      if (!imageFileList.value.some((f) => f.fileName === options.file.name)) {
        imageFileList.value.push(options.file);
      }
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error('文件上传失败，请重试');
      options.onError(error);
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 删除上传的文件
const handleImageRemove = (file: any) => {
  const targetIndex = imageFileList.value.findIndex(
    (item) => item.uid === file.uid || item.fileName === file.fileName
  );

  if (targetIndex !== -1) {
    imageFileList.value.splice(targetIndex, 1);
    // 清空表单中的imageUrl
    meetingGuest.value.imageUrl = '';
    console.log(`已移除文件: ${file.name}`, imageFileList.value);
  } else {
    console.warn('未找到待删除文件', file);
  }
};

const handleDelete = (id: number) => {
  Modal.confirm({
    title: '删除提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', { style: 'color:red;' }, '确定要删除'),
    onOk() {
      meetingGuestApi.remove(id)
        .then(() => {
          message.success('删除成功')
          listApiRun({
            miceInfoId: 1,
            pageNum: 1,
            pageSize: 50
          })
        })
    },
    onCancel() {
      console.log('Cancel');
    },
  });

}

// //下移
// const moveDown = async (record: IMeetingAgenda) => {
//   // console.log(record);
//   const index = dataSource.value.findIndex((item) => item.id === record.id)
//   // if (index == Number(dataSource.value.length) - 1) {
//   //   message.warning('当前为最后一行不可下移')
//   //   return
//   // }
//   if (index >= 0) {
//     const paramsDown = {
//       id: record.id,
//       sort: dataSource.value[index + 1].sort
//     }
//     const paramsUp = {
//       id: dataSource.value[index + 1].id,
//       sort: record.sort
//     }
//     // console.log(paramsDown, paramsUp);

//     await meetingGuestApi.moveUp(paramsDown)
//     await meetingGuestApi.moveUp(paramsUp)
//     await listApiRun({
//       miceId: Number(id),
//       pageNum: 1,
//       pageSize: 50
//     })
//   }
// }

// //上移
// const moveUp = async (record: IMeetingAgenda) => {
//   // console.log(record);
//   const index = dataSource.value.findIndex((item) => item.id === record.id)
//   // if (index == 0) {
//   //   message.warning('当前为第一行不可上移')
//   //   return
//   // }
//   if (index >= 0) {
//     const paramsUp = {
//       id: record.id,
//       sort: dataSource.value[index - 1].sort
//     }
//     const paramsDown = {
//       id: dataSource.value[index - 1].id,
//       sort: record.sort
//     }
//     // console.log(paramsDown, paramsUp);

//     await meetingGuestApi.moveUp(paramsDown)
//     await meetingGuestApi.moveUp(paramsUp)
//     await listApiRun({
//       miceId: Number(id),
//       pageNum: 1,
//       pageSize: 50
//     })
//   }
// }



</script>

<template>
  <div class="container">
    <div class="content">
      <div class="nav">
        会议首页/{{ router.currentRoute?.value.meta.title }}
      </div>
      <div class="main">
        <div class="main-top">
          <div class="top-title">
            {{ router.currentRoute?.value.meta.title }}
          </div>
          <ul class="top-right">
            <li class="" style="margin-right: 10px;">
              <a-button type="primary" @click="handleAdd">添加</a-button>
            </li>
            <li>
              <a-button @click="handleImport" style="margin-right: 10px;">导入</a-button>
            </li>
            <li>
              <a-button @click="handleExport" style="margin-right: 10px;" :loading="uploadLoading">导出</a-button>
            </li>
          </ul>
        </div>
        <div class="center-main">
          <div class="center-middle">
            <a-table :rowKey="(record: any) => record.id" :columns="columns" :data-source="dataSource"
              :pagination="pagination" @change="handleTableChange($event as any)" :scroll="{ x: 1280 }">
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.dataIndex === '_operator'">
                  <div>
                    <a-button type="link" @click="handleEdit(record)">编辑</a-button>
                    <a-button type="link" @click="handleDelete(record.id)">删除</a-button>
                    <!-- <a-button v-if="index != Number(dataSource.length) - 1" type="link"
                      @click="moveDown(record)">下移</a-button>
                    <a-button v-if="index != 0" type="link" @click="moveUp(record)">上移</a-button> -->
                  </div>
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </div>
    <Modal v-model:open="Visible" :title="meetingGuest.id ? '编辑嘉宾' : '添加嘉宾'" width="800px"
      :confirm-loading="confirmLoading" @ok="handleOk">
      <a-form ref="formRef" :rules="rules" :model="meetingGuest" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="嘉宾来源：" name="guestSource">
          <a-select v-model:value="meetingGuest.guestSource" placeholder="请选择嘉宾来源" allow-clear>
            <a-select-option v-for="(text, value) in ATTENDEE_TYPE_MAP" :key="value" :value="Number(value)">
              {{ text }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="姓名：" name="name">
          <a-input v-model:value="meetingGuest.name" placeholder="请输入姓名" allow-clear />
        </a-form-item>
        <a-form-item label="所属单位：" name="companyId">
          <a-select v-model:value="meetingGuest.companyId" placeholder="请选择所属单位" allow-clear>
            <a-select-option :value="1">海尔</a-select-option>
            <a-select-option :value="2">非海尔</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="照片：" name="imageUrl">
          <a-upload v-model:file-list="imageFileList" :custom-request="uploadRequest" :multiple="false" :max-count="1"
            @remove="handleImageRemove" accept=".jpg,.jpeg,.png,.gif,.bmp" list-type="picture-card">
            <div v-if="imageFileList.length < 1">
              <UploadOutlined />
              <div style="margin-top: 8px">上传照片</div>
            </div>
          </a-upload>
        </a-form-item>
        <a-form-item label="嘉宾头衔：" name="title">
          <a-input v-model:value="meetingGuest.title" placeholder="请输入嘉宾头衔" allow-clear />
        </a-form-item>
        <a-form-item label="嘉宾简介：" name="description">
          <a-textarea v-model:value="meetingGuest.description" placeholder="请输入嘉宾简介" :rows="4" allow-clear />
        </a-form-item>
        <a-form-item label="禁忌与偏好：" name="specialRequest">
          <a-textarea v-model:value="meetingGuest.specialRequest" placeholder="请输入禁忌与偏好" :rows="4" allow-clear />
        </a-form-item>
        <a-form-item label="备注：" name="remark">
          <a-textarea v-model:value="meetingGuest.remark" placeholder="请输入备注" :rows="4" allow-clear />
        </a-form-item>
        <a-form-item label="嘉宾排序：" name="sort">
          <a-input-number v-model:value="meetingGuest.sort" placeholder="请输入排序" style="width: 100%" />
        </a-form-item>
      </a-form>
    </Modal>

    <Modal v-model:open="importModel" title="导入参会人" width="35%" :confirm-loading="confirmLoading">
      <template #footer>
        <a-button key="back" @click="importModel = false">取消</a-button>
        <a-button key="submit" type="primary" @click="handleFileImport">导入</a-button>
      </template>
      <a-row :gutter="[16, 16]" style="margin-top: 20px;">
        <a-col :span="6" style="text-align: right;line-height: 32px;">
          上传文件：
        </a-col>
        <a-col :span="10">
          <input ref="fileInputRef" type="file" style="display: none;" accept=".xlsx,.xls,.csv"
            @change="handleFileChange" />
          <a-button type="primary" @click="triggerFileSelect" style="margin-right: 8px;">
            <UploadOutlined /> 选择文件
          </a-button>
        </a-col>
        <a-col :span="8" style="line-height: 32px;">
          <a-button type="link">下载导入模板</a-button>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="15" style="line-height: 32px;text-align: right;margin-top: 0px;">
          <span v-if="ConferenceDocuments">{{ ConferenceDocuments.name }}</span>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24" style="line-height: 32px;text-align: center;margin-top: 20px;">
          <img src="../../assets/image/orderList/warn.png" alt=""
            style="width: 17px;height: 17px;vertical-align: middle;">导入新数据后将覆盖<span
            style="color: red;">来源为录入</span>的全部数据
        </a-col>

      </a-row>
    </Modal>
  </div>
</template>
<style lang="scss" scoped>
* {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
}

ul,
p {
  margin-bottom: 0;
}

ul li {
  list-style: none;
}

:deep(.ant-select) {
  min-width: 100px;
}

.container {
  height: auto;
  width: 100%;
  background: #F6F7F9;
}

.content {
  width: 1280px;
  height: auto;
  margin: 0 auto;

  .main {
    width: 100%;
    background-color: #fff;

    .main-top {
      width: 100%;
      border: 1px solid #E5E6EB;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 18px 24px;

      .top-title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 20px;
        color: #1D2129;
        line-height: 28px;
        text-align: left;
        font-style: normal;
      }

      .top-right {
        display: flex;
        align-items: center;
        margin-bottom: 0 !important;

        .copy {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #1868DB;
          line-height: 20px;
          text-align: right;
          font-style: normal;

          img {
            width: 16px;
            height: 16px;
          }
        }
      }

    }

    .center-main {
      padding: 20px 24px;

      .center-top {
        display: flex;

        span {
          margin-left: 2px;
        }
      }

      .top-left {
        width: 109px;
        height: 32px;
        background: #1868DB;
        border-radius: 4px;
        line-height: 32px;
        text-align: center;
        font-size: 14px;
        color: #fff;
        margin-right: 12px;
      }

      .top-center {
        width: 185px;
        height: 32px;
        background: #F2F3F5;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        color: #1D2129;
        line-height: 22px;
        text-align: center;
        font-style: normal;

        .line {
          width: 1px;
          height: 18px;
          background: #E7E7E7;
        }
      }

      .center-middle {
        margin-top: 20px;
      }
    }
  }
}

.nav {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #86909C;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  padding: 16px 0px;
}
</style>