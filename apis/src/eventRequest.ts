// import { fetchEventSource, FetchEventSourceInit } from "@microsoft/fetch-event-source";
import { HeaderConstant } from '@haierbusiness-front/common-libs';
import { loadDataFromLocal } from '@haierbusiness-front/utils';

/**
 * EventSource 请求配置接口
 */
export interface EventSourceOptions<T> extends Omit<FetchEventSourceInit, 'onmessage'> {
    url: string;
    params?: Record<string, string | number | boolean>;
    onMessage: (data: T) => void;
    onClose?: () => void;
    onError?: (error: any) => void;
}

/**
 * 通用 EventSource 请求函数
 * @param options EventSource 请求配置
 */
export const eventGet = <T>(options: EventSourceOptions<T>): void => {
    const {
        url,
        params = {},
        onMessage,
        onClose,
        onError,
        headers = {},
        ...restOptions
    } = options;

    // 构建完整 URL 和参数
    const fullUrl = new URL(url, window.location.origin + window.location.pathname);

    // 添加参数
    Object.keys(params).forEach(key => {
        fullUrl.searchParams.append(key, String(params[key]));
    });

    // 添加认证 token
    const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false);

    // 合并 headers
    const requestHeaders = {
        [HeaderConstant.TOKEN_KEY.key]: token,
        ...headers
    };

    fetchEventSource(fullUrl.toString(), {
        method: 'GET',
        headers: requestHeaders,
        async onopen(response) {
            if (!response.ok) {
                throw new Error('请求失败');
            }
        },
        openWhenHidden: true,
        onmessage(ev) {
            try {
                const data: T = JSON.parse(ev.data);
                onMessage(data);
            } catch (error) {
                console.error('解析 EventSource 数据失败:', error);
                if (onError) {
                    onError(error);
                }
            }
        },
        onclose() {
            if (onClose) {
                onClose();
            }
        },
        onerror(err) {
            console.error('EventSource 错误:', err);
            if (onError) {
                onError(err);
            }
            throw err;
        },
        ...restOptions
    });
};
